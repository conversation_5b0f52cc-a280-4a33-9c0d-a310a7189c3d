import React from 'react';
import { cn } from '@/lib/utils';

interface SectionContainerProps {
  children: React.ReactNode;
  className?: string;
  containerClassName?: string;
  background?: 'default' | 'muted' | 'gradient' | 'transparent';
  padding?: 'sm' | 'md' | 'lg' | 'xl';
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '6xl' | '7xl' | 'full';
  withDivider?: boolean;
  id?: string;
}

export const SectionContainer: React.FC<SectionContainerProps> = ({
  children,
  className = "",
  containerClassName = "",
  background = 'default',
  padding = 'lg',
  maxWidth = '7xl',
  withDivider = false,
  id
}) => {
  // Background classes
  const backgroundClasses = {
    default: 'bg-background',
    muted: 'bg-gray-50 dark:bg-gray-900/50',
    gradient: 'bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900',
    transparent: 'bg-transparent'
  };

  // Padding classes
  const paddingClasses = {
    sm: 'py-8 sm:py-12',
    md: 'py-12 sm:py-16',
    lg: 'py-16 sm:py-20 lg:py-24',
    xl: 'py-20 sm:py-24 lg:py-32'
  };

  // Max width classes
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    '6xl': 'max-w-6xl',
    '7xl': 'max-w-7xl',
    full: 'max-w-full'
  };

  return (
    <section 
      id={id}
      className={cn(
        'relative',
        backgroundClasses[background],
        paddingClasses[padding],
        className
      )}
    >
      <div className={cn(
        'container mx-auto px-4 sm:px-6 lg:px-8',
        maxWidthClasses[maxWidth],
        containerClassName
      )}>
        {children}
      </div>
      
      {withDivider && (
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300 dark:via-gray-700 to-transparent opacity-50"></div>
      )}
    </section>
  );
};

export default SectionContainer;
